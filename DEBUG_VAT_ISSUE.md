# 🐛 Debug: Lỗi Thuế GTGT Wizard - Hướng dẫn Kiểm tra

## 🎯 Vấn đề

**Báo cáo:** Wizard bước 2 đã chọn 5% thuế, nhưng bước 3 vẫn hiển thị 10% thuế.

## 🔧 Thay đổi đã thực hiện

### **1. Thêm Debug Logging**
- Thêm `console.log()` trong `generateWizardReview()` để theo dõi dữ liệu
- Sửa hiển thị danh mục từ `data.category` thành `template.category`

### **2. Sửa Logic Hiển thị**
- **Trước:** `<div><strong>Danh mục:</strong> ${data.category}</div>`
- **Sau:** `<div><strong>Danh mục:</strong> ${template.category}</div>`

## 🧪 Cách Test và Debug

### **Bước 1: Mở Developer Tools**
1. **Mở file:** `client/public/standalone.html`
2. **Nhấn F12** để mở Developer Tools
3. **Chuyển tab Console** để xem log

### **Bước 2: Test Wizard Thu Dịch vụ**
1. **Click "💼 Thu cung cấp dịch vụ"**
2. **Bước 1:** Điền thông tin cơ bản
3. **Bước 2:** 
   - Điền khách hàng và loại dịch vụ
   - **Quan trọng:** Thay đổi thuế GTGT từ 5% sang 10% hoặc 0%
   - Click "Tiếp theo"
4. **Bước 3:** 
   - **Kiểm tra Console** (F12) xem log
   - **Kiểm tra hiển thị** thuế GTGT trên giao diện

### **Bước 3: Phân tích Log**
Trong Console sẽ hiển thị:
```javascript
Wizard Review Data: {
  type: "income",
  category: "Dịch vụ", 
  description: "...",
  amount: ...,
  date: "...",
  customerName: "...",
  serviceType: "...",
  vatRate: 5  // <-- Kiểm tra giá trị này
}

Template: {
  id: "service_revenue",
  title: "Thu cung cấp dịch vụ",
  hasVAT: true,
  defaultVATRate: 5,
  vatOptions: [5, 10, 0]
  // ...
}
```

## 🔍 Các Trường hợp Kiểm tra

### **Test Case 1: Thu cung cấp dịch vụ**
**Mong đợi:**
- Bước 2: Dropdown mặc định 5% (selected)
- Thay đổi thành 10%
- Bước 3: Hiển thị "Thuế GTGT: 10%"

**Kiểm tra:**
- [ ] Dropdown có 3 option: 5%, 10%, 0%
- [ ] Mặc định chọn 5%
- [ ] Có thể thay đổi được
- [ ] Bước 3 hiển thị đúng giá trị đã chọn

### **Test Case 2: Thu khác**
**Mong đợi:**
- Bước 2: Dropdown mặc định 5% (selected)
- Thay đổi thành 0%
- Bước 3: Hiển thị "Thuế GTGT: 0%"

**Kiểm tra:**
- [ ] Dropdown có 3 option: 5%, 10%, 0%
- [ ] Mặc định chọn 5%
- [ ] Có thể thay đổi được
- [ ] Bước 3 hiển thị đúng giá trị đã chọn

### **Test Case 3: Thu lãi tiền gửi**
**Mong đợi:**
- Bước 2: Box xám "Lãi tiền gửi không chịu thuế GTGT"
- Bước 3: Hiển thị lý do không có thuế

**Kiểm tra:**
- [ ] Không có dropdown thuế
- [ ] Hiển thị box xám với lý do
- [ ] Bước 3 hiển thị lý do không có thuế

## 🐛 Các Lỗi Có thể Gặp

### **Lỗi 1: Dropdown không thay đổi được**
**Nguyên nhân:** Event listener không hoạt động
**Giải pháp:** Kiểm tra console có lỗi JavaScript không

### **Lỗi 2: Bước 3 hiển thị sai**
**Nguyên nhân:** 
- `collectStep2Data()` không thu thập đúng
- `generateWizardReview()` hiển thị sai

**Debug:**
1. Kiểm tra log trong console
2. Xem `data.vatRate` có đúng không
3. Xem `template.hasVAT` có đúng không

### **Lỗi 3: Giá trị mặc định sai**
**Nguyên nhân:** Template configuration sai
**Kiểm tra:**
```javascript
service_revenue: {
  hasVAT: true,
  defaultVATRate: 5,  // <-- Phải là 5
  vatOptions: [5, 10, 0]  // <-- 5 phải đứng đầu
}
```

## 🔧 Cách Sửa Nếu Vẫn Lỗi

### **Nếu dropdown không hiển thị đúng mặc định:**
Kiểm tra hàm `generateVATField()`:
```javascript
const options = template.vatOptions.map(rate => 
    `<option value="${rate}" ${rate === template.defaultVATRate ? 'selected' : ''}>${rate}%</option>`
).join('');
```

### **Nếu bước 3 hiển thị sai:**
Kiểm tra hàm `collectStep2Data()`:
```javascript
if (template.hasVAT) {
    const vatRateEl = document.getElementById('vatRate');
    if (vatRateEl) {
        data.vatRate = parseFloat(vatRateEl.value);  // <-- Phải parseFloat
    }
}
```

### **Nếu vẫn không hoạt động:**
1. **Hard refresh:** Ctrl + F5
2. **Clear cache:** Xóa cache browser
3. **Kiểm tra file:** Đảm bảo file đã được lưu

## 📝 Checklist Debug

### **Trước khi test:**
- [ ] File `standalone.html` đã được mở
- [ ] Developer Tools đã mở (F12)
- [ ] Tab Console đã được chọn

### **Trong quá trình test:**
- [ ] Kiểm tra dropdown có 3 option
- [ ] Kiểm tra mặc định được chọn
- [ ] Thay đổi giá trị và kiểm tra
- [ ] Xem log trong console
- [ ] Kiểm tra hiển thị bước 3

### **Sau khi test:**
- [ ] Ghi lại kết quả
- [ ] Chụp ảnh màn hình nếu có lỗi
- [ ] Copy log từ console

## 🎯 Kết quả Mong đợi

**Nếu mọi thứ hoạt động đúng:**
1. **Bước 2:** Dropdown thuế GTGT mặc định 5%
2. **Thay đổi:** Có thể chọn 10% hoặc 0%
3. **Bước 3:** Hiển thị đúng giá trị đã chọn
4. **Console:** Log hiển thị `data.vatRate` đúng
5. **Hoàn thành:** Giao dịch được tạo với thuế đúng

**Nếu vẫn lỗi:**
- Gửi screenshot console log
- Gửi screenshot bước 2 và bước 3
- Mô tả chi tiết các bước đã thực hiện

## 🚀 Hướng dẫn Nhanh

**Test ngay:**
1. Mở `standalone.html`
2. F12 → Console
3. Click "💼 Thu cung cấp dịch vụ"
4. Điền bước 1 → Tiếp theo
5. Điền bước 2, **thay đổi thuế từ 5% → 10%** → Tiếp theo
6. Kiểm tra bước 3 có hiển thị "Thuế GTGT: 10%" không
7. Xem console log có đúng `vatRate: 10` không

**👉 Nếu vẫn lỗi, hãy gửi kết quả console log!**
