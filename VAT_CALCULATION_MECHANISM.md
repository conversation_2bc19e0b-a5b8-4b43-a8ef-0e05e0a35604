# 🧮 Cơ chế Tính thuế GTGT Thông minh - <PERSON><PERSON><PERSON> thành

## 🎯 Mục tiêu

Thiết kế cơ chế tính thuế GTGT **thông minh** dựa trên từng loại nghiệp vụ, tuân thủ quy định thuế Việt Nam:

- ✅ **C<PERSON> thuế GTGT:** D<PERSON><PERSON> vụ, b<PERSON>, cho thu<PERSON>, mua nguyên vật liệu, chi văn phòng
- ❌ **Không có thuế GTGT:** Chi lương, nộ<PERSON> thuế, lãi tiền gửi

## 📋 Phân loại Nghiệp vụ theo Thuế GTGT

### 🟢 **CÓ THUẾ GTGT** (hasVAT: true)

#### **📈 Thu nhập:**
1. **💼 Thu cung cấp dịch vụ** - Mặc định 10% (10%, 5%, 0%)
2. **🛒 <PERSON>hu bán hàng hóa** - Mặc định 10% (10%, 5%, 0%)  
3. **🏠 Thu cho thuê** - Mặc định 10% (10%, 5%, 0%)
4. **➕ Thu khác** - Mặc định 10% (10%, 5%, 0%)

#### **📉 Chi phí:**
1. **📦 Chi nguyên vật liệu** - Mặc định 10% (10%, 5%, 0%)
2. **🏢 Chi văn phòng** - Mặc định 10% (10%, 5%, 0%)
3. **➖ Chi khác** - Mặc định 10% (10%, 5%, 0%)

### 🔴 **KHÔNG CÓ THUẾ GTGT** (hasVAT: false)

#### **📈 Thu nhập:**
1. **💰 Thu lãi tiền gửi** - "Lãi tiền gửi không chịu thuế GTGT"

#### **📉 Chi phí:**
1. **👥 Chi lương** - "Chi lương không có thuế GTGT đầu vào"
2. **🏛️ Chi thuế** - "Nộp thuế không có thuế GTGT đầu vào"

## 🔧 Cách thức Hoạt động

### **1. Template Configuration**

Mỗi template có cấu hình thuế GTGT riêng:

```javascript
service_revenue: {
    id: 'service_revenue',
    title: 'Thu cung cấp dịch vụ',
    hasVAT: true,                    // Có thuế GTGT
    defaultVATRate: 10,              // Mặc định 10%
    vatOptions: [10, 5, 0]           // Tùy chọn: 10%, 5%, 0%
}

salary_expense: {
    id: 'salary_expense', 
    title: 'Chi lương',
    hasVAT: false,                   // Không có thuế GTGT
    vatNote: 'Chi lương không có thuế GTGT đầu vào'
}
```

### **2. Dynamic VAT Field Generation**

Hàm `generateVATField(template)` tự động tạo:

**Có thuế GTGT:**
```html
<div class="form-group">
    <label>Thuế suất GTGT</label>
    <select id="vatRate">
        <option value="10" selected>10%</option>
        <option value="5">5%</option>
        <option value="0">0%</option>
    </select>
</div>
```

**Không có thuế GTGT:**
```html
<div class="form-group">
    <label style="color: #666;">Thuế GTGT</label>
    <div style="padding: 0.75rem; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 6px; color: #666; font-style: italic;">
        Chi lương không có thuế GTGT đầu vào
    </div>
</div>
```

### **3. Smart Data Collection**

Hàm `collectStep2Data()` xử lý thông minh:

```javascript
if (template.hasVAT) {
    // Thu thập thuế suất từ dropdown
    data.vatRate = parseFloat(vatRateEl.value);
} else {
    // Đặt rõ ràng không có thuế
    data.vatRate = null;
    data.vatNote = template.vatNote;
}
```

### **4. Intelligent Review Display**

Bước 3 hiển thị thông tin thuế chính xác:

**Có thuế:** "Thuế GTGT: 5%"  
**Không có thuế:** "Thuế GTGT: _Chi lương không có thuế GTGT đầu vào_"

## 🎮 Demo Các Trường hợp

### **Test Case 1: Thu cung cấp dịch vụ** ✅
1. Click "💼 Thu cung cấp dịch vụ"
2. Bước 2: Có dropdown thuế GTGT, mặc định 10%
3. Đổi thành 5%
4. Bước 3: Hiển thị "Thuế GTGT: 5%"

### **Test Case 2: Chi lương** ✅
1. Click "👥 Chi lương"  
2. Bước 2: Hiển thị box xám "Chi lương không có thuế GTGT đầu vào"
3. Bước 3: Hiển thị "Thuế GTGT: _Chi lương không có thuế GTGT đầu vào_"

### **Test Case 3: Chi thuế** ✅
1. Click "🏛️ Chi thuế"
2. Bước 2: Hiển thị box xám "Nộp thuế không có thuế GTGT đầu vào"  
3. Bước 3: Hiển thị "Thuế GTGT: _Nộp thuế không có thuế GTGT đầu vào_"

### **Test Case 4: Thu lãi tiền gửi** ✅
1. Click "💰 Thu lãi tiền gửi"
2. Bước 2: Hiển thị box xám "Lãi tiền gửi không chịu thuế GTGT"
3. Bước 3: Hiển thị "Thuế GTGT: _Lãi tiền gửi không chịu thuế GTGT_"

### **Test Case 5: Chi nguyên vật liệu** ✅
1. Click "📦 Chi nguyên vật liệu"
2. Bước 2: Có dropdown thuế GTGT, mặc định 10%
3. Có thể chọn 10%, 5%, hoặc 0%
4. Bước 3: Hiển thị thuế suất đã chọn

## 🏛️ Tuân thủ Quy định Thuế Việt Nam

### **Nghiệp vụ CÓ thuế GTGT:**
- **Bán hàng hóa, dịch vụ:** Chịu thuế GTGT đầu ra
- **Mua nguyên vật liệu:** Có thuế GTGT đầu vào được khấu trừ
- **Chi văn phòng:** Có thuế GTGT đầu vào được khấu trừ
- **Cho thuê tài sản:** Chịu thuế GTGT

### **Nghiệp vụ KHÔNG có thuế GTGT:**
- **Chi lương:** Không phải hàng hóa/dịch vụ → Không có thuế GTGT
- **Nộp thuế:** Nghĩa vụ với nhà nước → Không có thuế GTGT  
- **Lãi tiền gửi:** Thu nhập tài chính → Không chịu thuế GTGT

### **Thuế suất GTGT Việt Nam:**
- **10%:** Hàng hóa, dịch vụ thông thường
- **5%:** Nước sạch, một số dịch vụ thiết yếu
- **0%:** Hàng xuất khẩu, dịch vụ giáo dục, y tế

## 💡 Lợi ích

### **Cho Người dùng:**
- **Tự động:** Hệ thống tự biết nghiệp vụ nào có/không có thuế
- **Chính xác:** Tuân thủ đúng quy định thuế Việt Nam
- **Rõ ràng:** Hiển thị lý do tại sao không có thuế
- **Linh hoạt:** Vẫn cho phép chọn thuế suất khi cần

### **Cho Kế toán:**
- **Chuẩn hóa:** Đảm bảo nhập liệu đúng quy định
- **Tiết kiệm thời gian:** Không cần suy nghĩ từng trường hợp
- **Giảm sai sót:** Hệ thống tự động xử lý logic thuế
- **Audit trail:** Ghi chú rõ ràng lý do có/không có thuế

## 📝 Tóm tắt Thay đổi Code

### **File: `client/public/standalone-app.js`**

**1. Cập nhật Transaction Templates:**
- ➕ Thêm `hasVAT`, `defaultVATRate`, `vatOptions`, `vatNote`

**2. Thêm Helper Function:**
- ➕ `generateVATField(template)` - Tạo field thuế thông minh

**3. Cập nhật Step Generators:**
- 🔧 Tất cả `generate*Step()` sử dụng `generateVATField()`

**4. Cập nhật Data Collection:**
- 🔧 `collectStep2Data()` xử lý VAT theo template config

**5. Cập nhật Review & Notes:**
- 🔧 `generateWizardReview()` hiển thị VAT thông minh
- 🔧 `generateTransactionNotes()` ghi chú VAT chính xác

## 🎉 Kết luận

**Cơ chế tính thuế GTGT thông minh đã hoàn thành:**

✅ **Tự động phân biệt** nghiệp vụ có/không có thuế GTGT  
✅ **Tuân thủ quy định** thuế Việt Nam  
✅ **Giao diện thông minh** hiển thị đúng theo từng trường hợp  
✅ **Ghi chú rõ ràng** lý do có/không có thuế  
✅ **Linh hoạt** cho phép chọn thuế suất khi cần  

**👉 Hãy test tất cả 10 nghiệp vụ để trải nghiệm cơ chế mới!**
