// Standalone Accounting App - Client-side with Google Sheets integration
class AccountingApp {
    constructor() {
        this.transactions = JSON.parse(localStorage.getItem('transactions') || '[]');
        this.categories = {
            income: [
                '<PERSON><PERSON><PERSON> thu bán hàng',
                '<PERSON><PERSON><PERSON> thu dịch vụ',
                '<PERSON><PERSON> nhập khác',
                '<PERSON><PERSON><PERSON> tiề<PERSON> gửi',
                '<PERSON>hu từ đầu tư'
            ],
            expense: [
                'Chi phí nguyên vật liệu',
                '<PERSON> phí nhân công',
                'Chi phí văn phòng',
                'Chi phí marketing',
                'Chi phí thuế',
                'Chi phí khác'
            ]
        };

        // Google Sheets configuration
        this.googleConfig = {
            apiKey: 'YOUR_API_KEY', // Sẽ được cấu hình
            clientId: 'YOUR_CLIENT_ID', // Sẽ được cấu hình
            discoveryDoc: 'https://sheets.googleapis.com/$discovery/rest?version=v4',
            scopes: 'https://www.googleapis.com/auth/spreadsheets'
        };

        this.googleAuth = null;
        this.spreadsheetId = localStorage.getItem('spreadsheetId');
        this.syncSettings = JSON.parse(localStorage.getItem('syncSettings') || '{"autoSync": "manual", "sheetName": "Kế toán TT133"}');

        // Transaction templates for wizard (simplified to core business operations)
        this.transactionTemplates = {
            // Income templates - 4 core types
            service_revenue: {
                id: 'service_revenue',
                title: 'Thu cung cấp dịch vụ',
                type: 'income',
                category: 'Dịch vụ',
                icon: '💼',
                description: 'Thu nhập từ cung cấp dịch vụ cho khách hàng',
                hasVAT: true,
                defaultVATRate: 5,
                vatOptions: [5, 10, 0],
                vatNote: 'Áp dụng phương pháp tính thuế GTGT trực tiếp'
            },
            interest_income: {
                id: 'interest_income',
                title: 'Thu lãi tiền gửi',
                type: 'income',
                category: 'Lãi tiền gửi',
                icon: '💰',
                description: 'Thu nhập từ lãi ngân hàng, lãi đầu tư',
                hasVAT: false,
                vatNote: 'Lãi tiền gửi không chịu thuế GTGT'
            },
            capital_contribution: {
                id: 'capital_contribution',
                title: 'Thu nhận vốn góp',
                type: 'income',
                category: 'Vốn góp',
                icon: '🏦',
                description: 'Thu nhập từ vốn góp của chủ sở hữu, cổ đông',
                hasVAT: false,
                vatNote: 'Vốn góp không chịu thuế GTGT'
            },
            other_income: {
                id: 'other_income',
                title: 'Thu khác',
                type: 'income',
                category: 'Thu khác',
                icon: '➕',
                description: 'Các khoản thu nhập khác',
                hasVAT: true,
                defaultVATRate: 5,
                vatOptions: [5, 10, 0],
                vatNote: 'Áp dụng phương pháp tính thuế GTGT trực tiếp nếu là dịch vụ'
            },

            // Expense templates - 4 core types
            salary_payment: {
                id: 'salary_payment',
                title: 'Chi trả lương',
                type: 'expense',
                category: 'Lương',
                icon: '👥',
                description: 'Chi phí lương, thưởng, phụ cấp cho nhân viên',
                hasVAT: false,
                vatNote: 'Doanh nghiệp không khấu trừ thuế GTGT đầu vào'
            },
            tax_payment: {
                id: 'tax_payment',
                title: 'Chi đóng thuế',
                type: 'expense',
                category: 'Thuế',
                icon: '🏛️',
                description: 'Chi phí đóng thuế GTGT, TNDN, TNCN, môn bài',
                hasVAT: false,
                vatNote: 'Doanh nghiệp không khấu trừ thuế GTGT đầu vào'
            },
            social_insurance: {
                id: 'social_insurance',
                title: 'Chi đóng BHXH',
                type: 'expense',
                category: 'BHXH',
                icon: '🛡️',
                description: 'Chi phí đóng bảo hiểm xã hội, y tế, thất nghiệp',
                hasVAT: false,
                vatNote: 'Doanh nghiệp không khấu trừ thuế GTGT đầu vào'
            },
            other_expense: {
                id: 'other_expense',
                title: 'Chi khác',
                type: 'expense',
                category: 'Chi khác',
                icon: '➖',
                description: 'Các khoản chi phí khác',
                hasVAT: false,
                vatNote: 'Doanh nghiệp không khấu trừ thuế GTGT đầu vào'
            }
        };

        // Current wizard state
        this.currentWizard = {
            type: null,
            template: null,
            step: 1,
            data: {}
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateDashboard();
        this.initGoogleAPI();
        this.updateGoogleSheetsUI();
    }

    setupEventListeners() {
        // No manual form listeners needed - using wizard only
    }



    saveTransactions() {
        localStorage.setItem('transactions', JSON.stringify(this.transactions));
    }

    updateDashboard() {
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();

        // Filter transactions for current month
        const monthlyTransactions = this.transactions.filter(t => {
            const transactionDate = new Date(t.date);
            return transactionDate.getMonth() === currentMonth &&
                   transactionDate.getFullYear() === currentYear;
        });

        // Calculate totals
        const totalIncome = monthlyTransactions
            .filter(t => t.type === 'income')
            .reduce((sum, t) => sum + t.amount, 0);

        const totalExpense = monthlyTransactions
            .filter(t => t.type === 'expense')
            .reduce((sum, t) => sum + t.amount, 0);

        const netProfit = totalIncome - totalExpense;

        // Update dashboard stats
        document.getElementById('totalIncome').textContent = this.formatCurrency(totalIncome);
        document.getElementById('totalExpense').textContent = this.formatCurrency(totalExpense);
        document.getElementById('netProfit').textContent = this.formatCurrency(netProfit);
        document.getElementById('transactionCount').textContent = monthlyTransactions.length;

        // Update recent transactions
        this.updateRecentTransactions();
        this.updateIncomeHistory();
        this.updateExpenseHistory();
    }

    updateRecentTransactions() {
        const container = document.getElementById('recentTransactions');
        const recentTransactions = this.transactions
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (recentTransactions.length === 0) {
            container.innerHTML = `
                <p style="text-align: center; color: #666; padding: 2rem;">
                    Chưa có giao dịch nào. Hãy thêm giao dịch đầu tiên!
                </p>
            `;
            return;
        }

        container.innerHTML = recentTransactions.map(transaction => `
            <div class="transaction-item">
                <div>
                    <div style="font-weight: 500;">${transaction.description}</div>
                    <div style="font-size: 0.9rem; color: #666;">
                        ${transaction.category} • ${this.formatDate(transaction.date)}
                    </div>
                </div>
                <div class="${transaction.type === 'income' ? 'amount-positive' : 'amount-negative'}">
                    ${transaction.type === 'income' ? '+' : '-'}${this.formatCurrency(transaction.amount)}
                </div>
            </div>
        `).join('');
    }

    updateIncomeHistory() {
        const container = document.getElementById('incomeHistory');
        if (!container) return;

        const incomeTransactions = this.transactions
            .filter(t => t.type === 'income')
            .sort((a, b) => new Date(b.date) - new Date(a.date));

        if (incomeTransactions.length === 0) {
            container.innerHTML = `
                <p style="text-align: center; color: #666; padding: 2rem;">
                    Chưa có giao dịch thu nào
                </p>
            `;
            return;
        }

        container.innerHTML = incomeTransactions.map(transaction => `
            <div class="transaction-item">
                <div>
                    <div style="font-weight: 500;">${transaction.description}</div>
                    <div style="font-size: 0.9rem; color: #666;">
                        ${transaction.category} • ${this.formatDate(transaction.date)}
                        ${transaction.notes ? ` • ${transaction.notes}` : ''}
                    </div>
                </div>
                <div class="amount-positive">
                    +${this.formatCurrency(transaction.amount)}
                </div>
            </div>
        `).join('');
    }

    updateExpenseHistory() {
        const container = document.getElementById('expenseHistory');
        if (!container) return;

        const expenseTransactions = this.transactions
            .filter(t => t.type === 'expense')
            .sort((a, b) => new Date(b.date) - new Date(a.date));

        if (expenseTransactions.length === 0) {
            container.innerHTML = `
                <p style="text-align: center; color: #666; padding: 2rem;">
                    Chưa có giao dịch chi nào
                </p>
            `;
            return;
        }

        container.innerHTML = expenseTransactions.map(transaction => `
            <div class="transaction-item">
                <div>
                    <div style="font-weight: 500;">${transaction.description}</div>
                    <div style="font-size: 0.9rem; color: #666;">
                        ${transaction.category} • ${this.formatDate(transaction.date)}
                        ${transaction.notes ? ` • ${transaction.notes}` : ''}
                    </div>
                </div>
                <div class="amount-negative">
                    -${this.formatCurrency(transaction.amount)}
                </div>
            </div>
        `).join('');
    }

    // Wizard methods
    openTransactionWizard(type, templateId) {
        const template = this.transactionTemplates[templateId];
        if (!template) {
            alert('Template không tồn tại!');
            return;
        }

        this.currentWizard = {
            type: type,
            template: template,
            step: 1,
            data: {
                type: type,
                category: template.category
            }
        };

        this.showWizardModal();
        this.updateWizardStep();
    }

    showWizardModal() {
        // Create wizard modal if it doesn't exist
        let modal = document.getElementById('transactionWizard');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'transactionWizard';
            modal.className = 'wizard-modal';
            modal.innerHTML = `
                <div class="wizard-overlay" onclick="closeWizard()"></div>
                <div class="wizard-content">
                    <div class="wizard-header">
                        <h2 id="wizardTitle"></h2>
                        <button class="wizard-close" onclick="closeWizard()">×</button>
                    </div>
                    <div class="wizard-progress">
                        <div class="progress-step active" data-step="1">1</div>
                        <div class="progress-line"></div>
                        <div class="progress-step" data-step="2">2</div>
                        <div class="progress-line"></div>
                        <div class="progress-step" data-step="3">3</div>
                    </div>
                    <div class="wizard-body">
                        <div id="wizardStepContent"></div>
                    </div>
                    <div class="wizard-footer">
                        <button id="wizardBackBtn" class="btn btn-secondary" onclick="previousStep()" style="display: none;">Quay lại</button>
                        <div style="flex: 1;"></div>
                        <button id="wizardNextBtn" class="btn btn-primary" onclick="nextStep()">Tiếp theo</button>
                        <button id="wizardFinishBtn" class="btn btn-success" onclick="finishWizard()" style="display: none;">Hoàn thành</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Add wizard styles
            this.addWizardStyles();
        }

        modal.classList.remove('hidden');
    }

    addWizardStyles() {
        if (document.getElementById('wizardStyles')) return;

        const style = document.createElement('style');
        style.id = 'wizardStyles';
        style.textContent = `
            .wizard-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .wizard-modal.hidden {
                display: none;
            }

            .wizard-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
            }

            .wizard-content {
                position: relative;
                background: white;
                border-radius: 12px;
                width: 90%;
                max-width: 600px;
                max-height: 90vh;
                overflow: hidden;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            }

            .wizard-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 1.5rem;
                border-bottom: 1px solid #eee;
                background: #f8f9fa;
            }

            .wizard-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .wizard-progress {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 1.5rem;
                background: #f8f9fa;
            }

            .progress-step {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: #e0e0e0;
                color: #666;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                transition: all 0.3s ease;
            }

            .progress-step.active {
                background: #007bff;
                color: white;
            }

            .progress-step.completed {
                background: #28a745;
                color: white;
            }

            .progress-line {
                width: 60px;
                height: 2px;
                background: #e0e0e0;
                transition: all 0.3s ease;
            }

            .progress-line.completed {
                background: #28a745;
            }

            .wizard-body {
                padding: 2rem;
                max-height: 400px;
                overflow-y: auto;
            }

            .wizard-footer {
                display: flex;
                align-items: center;
                padding: 1.5rem;
                border-top: 1px solid #eee;
                background: #f8f9fa;
            }
        `;
        document.head.appendChild(style);
    }

    updateAllTransactions() {
        const container = document.getElementById('allTransactions');
        const allTransactions = this.transactions
            .sort((a, b) => new Date(b.date) - new Date(a.date));

        if (allTransactions.length === 0) {
            container.innerHTML = `
                <p style="text-align: center; color: #666; padding: 2rem;">
                    Chưa có giao dịch nào
                </p>
            `;
            return;
        }

        container.innerHTML = allTransactions.map(transaction => `
            <div class="transaction-item">
                <div>
                    <div style="font-weight: 500;">${transaction.description}</div>
                    <div style="font-size: 0.9rem; color: #666;">
                        ${transaction.category} • ${this.formatDate(transaction.date)}
                        ${transaction.notes ? ` • ${transaction.notes}` : ''}
                    </div>
                </div>
                <div class="${transaction.type === 'income' ? 'amount-positive' : 'amount-negative'}">
                    ${transaction.type === 'income' ? '+' : '-'}${this.formatCurrency(transaction.amount)}
                </div>
            </div>
        `).join('');
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('vi-VN');
    }

    // Google Sheets Integration Methods
    async initGoogleAPI() {
        try {
            // For demo purposes, we'll use a simplified approach
            // In production, you would need to set up proper Google API credentials
            console.log('Google API initialized (demo mode)');
        } catch (error) {
            console.error('Failed to initialize Google API:', error);
        }
    }

    updateGoogleSheetsUI() {
        const isConnected = !!this.spreadsheetId;
        document.getElementById('sheets-disconnected').classList.toggle('hidden', isConnected);
        document.getElementById('sheets-connected').classList.toggle('hidden', !isConnected);

        if (isConnected) {
            const lastSync = localStorage.getItem('lastSync');
            const syncInfo = lastSync ?
                `Lần cuối: ${new Date(lastSync).toLocaleString('vi-VN')}` :
                'Chưa đồng bộ lần nào';
            document.getElementById('sync-info').textContent = syncInfo;
        }

        // Update sync settings
        document.getElementById('autoSync').value = this.syncSettings.autoSync;
        document.getElementById('sheetName').value = this.syncSettings.sheetName;
    }

    async connectGoogleSheets() {
        try {
            // Demo implementation - in real app, this would use Google OAuth
            const confirmed = confirm(
                'Tính năng này đang ở chế độ demo.\n\n' +
                'Trong phiên bản thực tế, bạn sẽ:\n' +
                '1. Đăng nhập Google\n' +
                '2. Cấp quyền truy cập Google Sheets\n' +
                '3. Tự động tạo spreadsheet mới\n\n' +
                'Bạn có muốn tiếp tục với demo không?'
            );

            if (!confirmed) return;

            // Simulate creating a spreadsheet
            const mockSpreadsheetId = 'demo_' + Date.now();
            this.spreadsheetId = mockSpreadsheetId;
            localStorage.setItem('spreadsheetId', mockSpreadsheetId);

            // Create demo spreadsheet structure
            await this.createSpreadsheetStructure();

            this.updateGoogleSheetsUI();
            alert('✅ Đã kết nối thành công với Google Sheets (Demo)!\n\nTrong phiên bản thực tế, một Google Sheet mới sẽ được tạo tự động.');

        } catch (error) {
            console.error('Error connecting to Google Sheets:', error);
            alert('❌ Lỗi kết nối Google Sheets: ' + error.message);
        }
    }

    async createSpreadsheetStructure() {
        // In real implementation, this would create actual Google Sheets
        const structure = {
            title: this.syncSettings.sheetName,
            sheets: [
                {
                    title: 'Thu nhập',
                    headers: ['Ngày', 'Mô tả', 'Danh mục', 'Số tiền', 'Ghi chú', 'Tham chiếu']
                },
                {
                    title: 'Chi phí',
                    headers: ['Ngày', 'Mô tả', 'Danh mục', 'Số tiền', 'Ghi chú', 'Tham chiếu']
                },
                {
                    title: 'Tổng hợp',
                    headers: ['Tháng', 'Tổng thu', 'Tổng chi', 'Lợi nhuận', 'Số giao dịch']
                }
            ]
        };

        localStorage.setItem('spreadsheetStructure', JSON.stringify(structure));
        console.log('Created spreadsheet structure:', structure);
    }

    async syncToGoogleSheets() {
        if (!this.spreadsheetId) {
            alert('Chưa kết nối Google Sheets!');
            return;
        }

        try {
            // Show loading
            const syncButton = document.querySelector('button[onclick="syncToSheets()"]');
            const originalText = syncButton.innerHTML;
            syncButton.innerHTML = '<span class="material-icons">sync</span> Đang đồng bộ...';
            syncButton.disabled = true;

            // Simulate sync process
            await this.simulateSync();

            // Update last sync time
            const now = new Date().toISOString();
            localStorage.setItem('lastSync', now);

            // Update UI
            this.updateGoogleSheetsUI();

            // Restore button
            syncButton.innerHTML = originalText;
            syncButton.disabled = false;

            alert('✅ Đồng bộ thành công!\n\n' +
                  `Đã đồng bộ ${this.transactions.length} giao dịch lên Google Sheets.`);

        } catch (error) {
            console.error('Sync error:', error);
            alert('❌ Lỗi đồng bộ: ' + error.message);
        }
    }

    async simulateSync() {
        // Simulate API calls with delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // In real implementation, this would:
        // 1. Clear existing data in sheets
        // 2. Upload income transactions to "Thu nhập" sheet
        // 3. Upload expense transactions to "Chi phí" sheet
        // 4. Generate summary data for "Tổng hợp" sheet

        const incomeTransactions = this.transactions.filter(t => t.type === 'income');
        const expenseTransactions = this.transactions.filter(t => t.type === 'expense');

        console.log('Syncing to Google Sheets:', {
            income: incomeTransactions.length,
            expense: expenseTransactions.length,
            total: this.transactions.length
        });

        // Store sync data for demo
        localStorage.setItem('lastSyncData', JSON.stringify({
            timestamp: new Date().toISOString(),
            transactionCount: this.transactions.length,
            incomeCount: incomeTransactions.length,
            expenseCount: expenseTransactions.length
        }));
    }

    openGoogleSheet() {
        if (!this.spreadsheetId) {
            alert('Chưa kết nối Google Sheets!');
            return;
        }

        // In real implementation, this would open the actual Google Sheet
        const demoUrl = `https://docs.google.com/spreadsheets/d/${this.spreadsheetId}/edit`;

        alert('📊 Trong phiên bản thực tế, Google Sheet sẽ được mở tại:\n\n' + demoUrl + '\n\n' +
              'Demo: Hiển thị cấu trúc spreadsheet trong console.');

        // Show structure in console for demo
        const structure = JSON.parse(localStorage.getItem('spreadsheetStructure') || '{}');
        console.log('Google Sheet Structure:', structure);
        console.log('Current Data:', this.transactions);
    }

    disconnectGoogleSheets() {
        const confirmed = confirm('Bạn có chắc muốn ngắt kết nối Google Sheets?\n\nDữ liệu local sẽ được giữ nguyên.');

        if (confirmed) {
            this.spreadsheetId = null;
            localStorage.removeItem('spreadsheetId');
            localStorage.removeItem('lastSync');
            localStorage.removeItem('spreadsheetStructure');
            localStorage.removeItem('lastSyncData');

            this.updateGoogleSheetsUI();
            alert('✅ Đã ngắt kết nối Google Sheets thành công!');
        }
    }

    updateSyncSettings() {
        this.syncSettings.autoSync = document.getElementById('autoSync').value;
        this.syncSettings.sheetName = document.getElementById('sheetName').value;

        localStorage.setItem('syncSettings', JSON.stringify(this.syncSettings));

        alert('✅ Đã cập nhật cài đặt đồng bộ!');
    }

    // Wizard step management
    updateWizardStep() {
        const step = this.currentWizard.step;
        const template = this.currentWizard.template;

        // Update title
        document.getElementById('wizardTitle').textContent = `${template.icon} ${template.title}`;

        // Update progress
        document.querySelectorAll('.progress-step').forEach((el, index) => {
            const stepNum = index + 1;
            el.classList.toggle('active', stepNum === step);
            el.classList.toggle('completed', stepNum < step);
        });

        document.querySelectorAll('.progress-line').forEach((el, index) => {
            el.classList.toggle('completed', index + 1 < step);
        });

        // Update buttons
        document.getElementById('wizardBackBtn').style.display = step > 1 ? 'block' : 'none';
        document.getElementById('wizardNextBtn').style.display = step < 3 ? 'block' : 'none';
        document.getElementById('wizardFinishBtn').style.display = step === 3 ? 'block' : 'none';

        // Update content
        this.updateWizardContent();

        // Validate current step
        setTimeout(() => this.validateWizardStep(), 100);
    }

    updateWizardContent() {
        const step = this.currentWizard.step;
        const container = document.getElementById('wizardStepContent');

        if (step === 1) {
            container.innerHTML = this.generateStep1Content();
        } else if (step === 2) {
            container.innerHTML = this.generateStep2Content();
        } else if (step === 3) {
            container.innerHTML = this.generateStep3Content();
        }

        // Add event listeners for validation
        container.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('input', () => this.validateWizardStep());
            input.addEventListener('change', () => this.validateWizardStep());
        });
    }

    generateStep1Content() {
        const today = new Date().toISOString().split('T')[0];
        return `
            <h3>Bước 1: Thông tin cơ bản</h3>
            <div class="transaction-form">
                <div class="form-group">
                    <label>Mô tả giao dịch *</label>
                    <input type="text" id="wizardDescription" placeholder="Nhập mô tả chi tiết" required>
                </div>
                <div class="form-group">
                    <label>Số tiền (VND) *</label>
                    <input type="number" id="wizardAmount" placeholder="0" min="1" required>
                </div>
                <div class="form-group">
                    <label>Ngày giao dịch *</label>
                    <input type="date" id="wizardDate" value="${today}" required>
                </div>
            </div>
        `;
    }

    generateStep2Content() {
        const template = this.currentWizard.template;

        switch (template.id) {
            case 'service_revenue':
                return this.generateServiceRevenueStep();
            case 'interest_income':
                return this.generateInterestIncomeStep();
            case 'capital_contribution':
                return this.generateCapitalContributionStep();
            case 'salary_payment':
                return this.generateSalaryPaymentStep();
            case 'tax_payment':
                return this.generateTaxPaymentStep();
            case 'social_insurance':
                return this.generateSocialInsuranceStep();
            default:
                return this.generateGenericStep();
        }
    }

    generateStep3Content() {
        return `
            <h3>Bước 3: Xác nhận thông tin</h3>
            <div id="wizardReview"></div>
        `;
    }

    // Wizard navigation
    nextStep() {
        if (!this.validateWizardStep()) return;

        this.collectStepData();
        this.currentWizard.step++;
        this.updateWizardStep();

        if (this.currentWizard.step === 3) {
            // Generate review with fresh data collection
            this.generateWizardReview();
        }
    }

    previousStep() {
        if (this.currentWizard.step > 1) {
            this.currentWizard.step--;
            this.updateWizardStep();
        }
    }

    validateWizardStep() {
        const step = this.currentWizard.step;
        let isValid = true;

        if (step === 1) {
            const description = document.getElementById('wizardDescription')?.value.trim();
            const amount = parseFloat(document.getElementById('wizardAmount')?.value);
            const date = document.getElementById('wizardDate')?.value;

            isValid = description && amount > 0 && date;
        } else if (step === 2) {
            isValid = this.validateStep2();
        }

        const nextBtn = document.getElementById('wizardNextBtn');
        const finishBtn = document.getElementById('wizardFinishBtn');

        if (nextBtn) nextBtn.disabled = !isValid;
        if (finishBtn) finishBtn.disabled = !isValid;

        return isValid;
    }

    validateStep2() {
        const template = this.currentWizard.template;

        switch (template.id) {
            case 'service_revenue':
                return document.getElementById('customerName')?.value.trim() &&
                       document.getElementById('serviceType')?.value;
            case 'product_sales':
                return document.getElementById('productName')?.value.trim() &&
                       document.getElementById('quantity')?.value;
            case 'salary_expense':
                return document.getElementById('employeeName')?.value.trim() &&
                       document.getElementById('salaryType')?.value;
            case 'tax_payment':
                return document.getElementById('taxType')?.value;
            case 'material_expense':
                return document.getElementById('materialName')?.value.trim() &&
                       document.getElementById('supplierName')?.value.trim();
            default:
                return true;
        }
    }

    collectStepData() {
        const step = this.currentWizard.step;

        if (step === 1) {
            this.currentWizard.data.description = document.getElementById('wizardDescription').value.trim();
            this.currentWizard.data.amount = parseFloat(document.getElementById('wizardAmount').value);
            this.currentWizard.data.date = document.getElementById('wizardDate').value;
        } else if (step === 2) {
            this.collectStep2Data();
        }
    }

    collectStep2Data() {
        const template = this.currentWizard.template;
        const data = this.currentWizard.data;

        // VAT handling based on template configuration
        if (template.hasVAT) {
            const vatRateEl = document.getElementById('vatRate');
            if (vatRateEl) {
                data.vatRate = parseFloat(vatRateEl.value);
                console.log('Collected VAT Rate:', data.vatRate, 'from element value:', vatRateEl.value);
            } else {
                console.log('VAT Rate element not found!');
            }
        } else {
            // For non-VAT transactions, explicitly set vatRate to null
            data.vatRate = null;
            data.vatNote = template.vatNote;
            console.log('No VAT for this template:', template.id);
        }

        // Template-specific fields
        switch (template.id) {
            case 'service_revenue':
                data.customerName = document.getElementById('customerName')?.value.trim();
                data.serviceType = document.getElementById('serviceType')?.value;
                break;
            case 'interest_income':
                data.bankName = document.getElementById('bankName')?.value.trim();
                break;
            case 'capital_contribution':
                data.contributorName = document.getElementById('contributorName')?.value.trim();
                data.contributionType = document.getElementById('contributionType')?.value;
                break;
            case 'salary_payment':
                data.employeeName = document.getElementById('employeeName')?.value.trim();
                data.salaryType = document.getElementById('salaryType')?.value;
                data.salaryPeriod = document.getElementById('salaryPeriod')?.value.trim();
                break;
            case 'tax_payment':
                data.taxType = document.getElementById('taxType')?.value;
                data.taxPeriod = document.getElementById('taxPeriod')?.value.trim();
                break;
            case 'social_insurance':
                data.insuranceType = document.getElementById('insuranceType')?.value;
                data.insurancePeriod = document.getElementById('insurancePeriod')?.value.trim();
                data.employeeCount = parseInt(document.getElementById('employeeCount')?.value) || null;
                break;
            default:
                // For generic step
                data.additionalNotes = document.getElementById('additionalNotes')?.value.trim();
                break;
        }
    }

    generateWizardReview() {
        const data = this.currentWizard.data;
        const template = this.currentWizard.template;

        // Force fresh data collection to ensure accuracy
        const vatRateEl = document.getElementById('vatRate');
        if (template.hasVAT && vatRateEl) {
            data.vatRate = parseFloat(vatRateEl.value);
            console.log('🔄 Fresh VAT Rate collected:', data.vatRate);
        }

        // Debug: Log current data to console
        console.log('📋 Final Wizard Review Data:', data);

        let additionalInfo = '';
        if (data.customerName) additionalInfo += `<div><strong>Khách hàng:</strong> ${data.customerName}</div>`;
        if (data.employeeName) additionalInfo += `<div><strong>Nhân viên:</strong> ${data.employeeName}</div>`;
        if (data.supplierName) additionalInfo += `<div><strong>Nhà cung cấp:</strong> ${data.supplierName}</div>`;
        if (data.serviceType) additionalInfo += `<div><strong>Loại dịch vụ:</strong> ${data.serviceType}</div>`;
        if (data.bankName) additionalInfo += `<div><strong>Ngân hàng:</strong> ${data.bankName}</div>`;
        if (data.contributorName) additionalInfo += `<div><strong>Người góp vốn:</strong> ${data.contributorName}</div>`;
        if (data.contributionType) additionalInfo += `<div><strong>Loại vốn góp:</strong> ${data.contributionType}</div>`;
        if (data.salaryPeriod) additionalInfo += `<div><strong>Kỳ lương:</strong> ${data.salaryPeriod}</div>`;
        if (data.taxPeriod) additionalInfo += `<div><strong>Kỳ thuế:</strong> ${data.taxPeriod}</div>`;
        if (data.insuranceType) additionalInfo += `<div><strong>Loại bảo hiểm:</strong> ${data.insuranceType}</div>`;
        if (data.insurancePeriod) additionalInfo += `<div><strong>Kỳ đóng BHXH:</strong> ${data.insurancePeriod}</div>`;
        if (data.employeeCount) additionalInfo += `<div><strong>Số nhân viên:</strong> ${data.employeeCount}</div>`;
        if (data.taxType) additionalInfo += `<div><strong>Loại thuế:</strong> ${data.taxType}</div>`;

        // VAT information display - use fresh data
        if (template.hasVAT && data.vatRate !== undefined && data.vatRate !== null) {
            additionalInfo += `<div><strong>Thuế GTGT:</strong> ${data.vatRate}%</div>`;
            console.log('✅ Displaying VAT Rate:', data.vatRate + '%');
        } else if (!template.hasVAT && data.vatNote) {
            additionalInfo += `<div><strong>Thuế GTGT:</strong> <em style="color: #666;">${data.vatNote}</em></div>`;
        }

        let reviewContent = `
            <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
                <h4>📋 Thông tin giao dịch</h4>
                <div style="margin-top: 1rem; line-height: 1.6;">
                    <div><strong>Loại:</strong> ${template.title}</div>
                    <div><strong>Mô tả:</strong> ${data.description}</div>
                    <div><strong>Số tiền:</strong> ${this.formatCurrency(data.amount)}</div>
                    <div><strong>Ngày:</strong> ${this.formatDate(data.date)}</div>
                    <div><strong>Danh mục:</strong> ${template.category}</div>
                    ${additionalInfo}
                </div>
                <div style="margin-top: 1rem; padding: 1rem; background: white; border-radius: 6px; border-left: 4px solid #007bff;">
                    <small><strong>Lưu ý:</strong> Vui lòng kiểm tra kỹ thông tin trước khi hoàn thành.</small>
                </div>
            </div>
        `;

        document.getElementById('wizardReview').innerHTML = reviewContent;
    }

    finishWizard() {
        if (!this.validateWizardStep()) return;

        this.collectStepData();

        // Create transaction
        const transactionData = {
            id: Date.now(),
            ...this.currentWizard.data,
            notes: this.generateTransactionNotes(),
            createdAt: new Date().toISOString()
        };

        this.transactions.push(transactionData);
        this.saveTransactions();
        this.updateDashboard();

        // Auto sync if enabled
        if (this.syncSettings.autoSync === 'immediate' && this.spreadsheetId) {
            this.syncToGoogleSheets();
        }

        this.closeWizard();

        alert('✅ Đã thêm giao dịch thành công!');
        showSection('transactions');
    }

    generateTransactionNotes() {
        const data = this.currentWizard.data;
        let notes = [];

        if (data.customerName) notes.push(`Khách hàng: ${data.customerName}`);
        if (data.employeeName) notes.push(`Nhân viên: ${data.employeeName}`);
        if (data.supplierName) notes.push(`Nhà cung cấp: ${data.supplierName}`);
        if (data.serviceType) notes.push(`Dịch vụ: ${data.serviceType}`);
        if (data.bankName) notes.push(`Ngân hàng: ${data.bankName}`);
        if (data.contributorName) notes.push(`Người góp vốn: ${data.contributorName}`);
        if (data.contributionType) notes.push(`Loại vốn góp: ${data.contributionType}`);
        if (data.salaryPeriod) notes.push(`Kỳ lương: ${data.salaryPeriod}`);
        if (data.taxPeriod) notes.push(`Kỳ thuế: ${data.taxPeriod}`);
        if (data.insuranceType) notes.push(`Loại bảo hiểm: ${data.insuranceType}`);
        if (data.insurancePeriod) notes.push(`Kỳ đóng BHXH: ${data.insurancePeriod}`);
        if (data.employeeCount) notes.push(`Số nhân viên: ${data.employeeCount}`);
        if (data.taxType) notes.push(`Loại thuế: ${data.taxType}`);
        // VAT information in notes
        const template = this.currentWizard.template;
        if (template.hasVAT && data.vatRate !== undefined && data.vatRate !== null) {
            notes.push(`Thuế GTGT: ${data.vatRate}%`);
        } else if (!template.hasVAT && data.vatNote) {
            notes.push(`Thuế GTGT: ${data.vatNote}`);
        }

        return notes.join(' | ');
    }

    closeWizard() {
        const modal = document.getElementById('transactionWizard');
        if (modal) {
            modal.classList.add('hidden');
        }

        this.currentWizard = {
            type: null,
            template: null,
            step: 1,
            data: {}
        };
    }

    // Template-specific step generators (simplified)
    generateServiceRevenueStep() {
        const template = this.currentWizard.template;
        return `
            <h3>Bước 2: Thông tin dịch vụ</h3>
            <div class="transaction-form">
                <div class="form-group">
                    <label>Tên khách hàng *</label>
                    <input type="text" id="customerName" placeholder="Tên công ty hoặc cá nhân" required>
                </div>
                <div class="form-group">
                    <label>Loại dịch vụ *</label>
                    <select id="serviceType" required>
                        <option value="">Chọn loại dịch vụ</option>
                        <option value="consulting">Tư vấn</option>
                        <option value="design">Thiết kế</option>
                        <option value="development">Phát triển</option>
                        <option value="other">Khác</option>
                    </select>
                </div>
                ${this.generateVATField(template)}
            </div>
        `;
    }

    generateCapitalContributionStep() {
        const template = this.currentWizard.template;
        return `
            <h3>Bước 2: Thông tin vốn góp</h3>
            <div class="transaction-form">
                <div class="form-group">
                    <label>Tên người góp vốn *</label>
                    <input type="text" id="contributorName" placeholder="Tên chủ sở hữu/cổ đông" required>
                </div>
                <div class="form-group">
                    <label>Loại vốn góp *</label>
                    <select id="contributionType" required>
                        <option value="">Chọn loại vốn góp</option>
                        <option value="initial">Vốn góp ban đầu</option>
                        <option value="additional">Vốn góp bổ sung</option>
                        <option value="share_capital">Vốn cổ phần</option>
                        <option value="other">Khác</option>
                    </select>
                </div>
                ${this.generateVATField(template)}
            </div>
        `;
    }

    generateInterestIncomeStep() {
        const template = this.currentWizard.template;
        return `
            <h3>Bước 2: Thông tin lãi suất</h3>
            <div class="transaction-form">
                <div class="form-group">
                    <label>Ngân hàng/Tổ chức</label>
                    <input type="text" id="bankName" placeholder="Tên ngân hàng">
                </div>
                ${this.generateVATField(template)}
            </div>
        `;
    }

    generateSalaryPaymentStep() {
        const template = this.currentWizard.template;
        return `
            <h3>Bước 2: Thông tin trả lương</h3>
            <div class="transaction-form">
                <div class="form-group">
                    <label>Tên nhân viên *</label>
                    <input type="text" id="employeeName" placeholder="Họ tên nhân viên" required>
                </div>
                <div class="form-group">
                    <label>Loại lương *</label>
                    <select id="salaryType" required>
                        <option value="">Chọn loại</option>
                        <option value="basic">Lương cơ bản</option>
                        <option value="bonus">Thưởng</option>
                        <option value="allowance">Phụ cấp</option>
                        <option value="overtime">Làm thêm giờ</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Kỳ lương</label>
                    <input type="text" id="salaryPeriod" placeholder="Tháng/năm (VD: 12/2024)">
                </div>
                ${this.generateVATField(template)}
            </div>
        `;
    }

    generateTaxPaymentStep() {
        const template = this.currentWizard.template;
        return `
            <h3>Bước 2: Thông tin đóng thuế</h3>
            <div class="transaction-form">
                <div class="form-group">
                    <label>Loại thuế *</label>
                    <select id="taxType" required>
                        <option value="">Chọn loại thuế</option>
                        <option value="VAT">Thuế GTGT</option>
                        <option value="CIT">Thuế TNDN</option>
                        <option value="PIT">Thuế TNCN</option>
                        <option value="license">Thuế môn bài</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Kỳ thuế</label>
                    <input type="text" id="taxPeriod" placeholder="Tháng/quý/năm (VD: Q4/2024)">
                </div>
                ${this.generateVATField(template)}
            </div>
        `;
    }

    generateSocialInsuranceStep() {
        const template = this.currentWizard.template;
        return `
            <h3>Bước 2: Thông tin đóng BHXH</h3>
            <div class="transaction-form">
                <div class="form-group">
                    <label>Loại bảo hiểm *</label>
                    <select id="insuranceType" required>
                        <option value="">Chọn loại bảo hiểm</option>
                        <option value="social">Bảo hiểm xã hội</option>
                        <option value="health">Bảo hiểm y tế</option>
                        <option value="unemployment">Bảo hiểm thất nghiệp</option>
                        <option value="accident">Bảo hiểm tai nạn lao động</option>
                        <option value="all">Tất cả các loại</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Kỳ đóng</label>
                    <input type="text" id="insurancePeriod" placeholder="Tháng/năm (VD: 12/2024)">
                </div>
                <div class="form-group">
                    <label>Số lượng nhân viên</label>
                    <input type="number" id="employeeCount" placeholder="Số nhân viên tham gia" min="1">
                </div>
                ${this.generateVATField(template)}
            </div>
        `;
    }

    generateGenericStep() {
        const template = this.currentWizard.template;
        return `
            <h3>Bước 2: Thông tin bổ sung</h3>
            <div class="transaction-form">
                <div class="form-group">
                    <label>Ghi chú</label>
                    <textarea id="additionalNotes" placeholder="Thông tin bổ sung" rows="3"></textarea>
                </div>
                ${this.generateVATField(template)}
            </div>
        `;
    }

    // Helper function to generate VAT field based on template configuration
    generateVATField(template) {
        if (!template.hasVAT) {
            return `
                <div class="form-group">
                    <label style="color: #666;">Thuế GTGT</label>
                    <div style="padding: 0.75rem; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 6px; color: #666; font-style: italic;">
                        ${template.vatNote}
                    </div>
                </div>
            `;
        }

        console.log('Generating VAT field for template:', template.id, 'defaultVATRate:', template.defaultVATRate);

        const options = template.vatOptions.map(rate => {
            const isSelected = rate === template.defaultVATRate;
            console.log(`Option ${rate}%: selected = ${isSelected}`);
            return `<option value="${rate}" ${isSelected ? 'selected' : ''}>${rate}%</option>`;
        }).join('');

        return `
            <div class="form-group">
                <label>Thuế suất GTGT</label>
                <select id="vatRate" onchange="console.log('VAT Rate changed to:', this.value)">
                    ${options}
                </select>
            </div>
        `;
    }
}

// Navigation functions
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.add('hidden');
    });

    // Show selected section
    document.getElementById(sectionId).classList.remove('hidden');

    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    event.target.closest('.nav-link').classList.add('active');
}

// Tax calculation function
function calculateVAT() {
    const revenue = parseFloat(document.getElementById('revenue').value) || 0;
    const vatRate = parseFloat(document.getElementById('vatRate').value) || 0;

    if (revenue <= 0) {
        alert('Vui lòng nhập doanh thu hợp lệ!');
        return;
    }

    const vatAmount = revenue * (vatRate / 100);
    const totalAmount = revenue + vatAmount;

    const resultContainer = document.getElementById('vatResult');
    resultContainer.innerHTML = `
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid #3498db;">
            <h4>Kết quả tính thuế GTGT:</h4>
            <p><strong>Doanh thu chưa thuế:</strong> ${app.formatCurrency(revenue)}</p>
            <p><strong>Thuế GTGT (${vatRate}%):</strong> ${app.formatCurrency(vatAmount)}</p>
            <p><strong>Tổng tiền phải thu:</strong> ${app.formatCurrency(totalAmount)}</p>
            <hr style="margin: 1rem 0;">
            <p><em>Công thức: Doanh thu × ${vatRate}% = ${app.formatCurrency(vatAmount)}</em></p>
        </div>
    `;
}

// Wizard functions (called from HTML)
function openTransactionWizard(type, templateId) {
    app.openTransactionWizard(type, templateId);
}

function nextStep() {
    app.nextStep();
}

function previousStep() {
    app.previousStep();
}

function finishWizard() {
    app.finishWizard();
}

function closeWizard() {
    app.closeWizard();
}

// Google Sheets functions (called from HTML)
function connectGoogleSheets() {
    app.connectGoogleSheets();
}

function syncToSheets() {
    app.syncToGoogleSheets();
}

function openGoogleSheet() {
    app.openGoogleSheet();
}

function disconnectGoogleSheets() {
    app.disconnectGoogleSheets();
}

function updateSyncSettings() {
    app.updateSyncSettings();
}

// Initialize app when page loads
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new AccountingApp();

    // Show welcome message for first-time users
    if (app.transactions.length === 0) {
        setTimeout(() => {
            alert('Chào mừng bạn đến với App Kế toán TT 133!\n\n' +
                  'Phiên bản này có tích hợp Google Sheets!\n\n' +
                  '✨ Tính năng mới:\n' +
                  '• Đồng bộ dữ liệu lên Google Sheets\n' +
                  '• Tự động tạo báo cáo\n' +
                  '• Truy cập từ mọi nơi\n\n' +
                  'Hãy bắt đầu bằng cách thêm giao dịch đầu tiên!');
        }, 1000);
    }
});
