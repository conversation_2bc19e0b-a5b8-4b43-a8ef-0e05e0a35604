<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test VAT Dropdown</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        select, input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .debug {
            margin-top: 20px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test VAT Dropdown - Thu cung cấp dịch vụ</h1>
        <p>Test này mô phỏng chính xác wizard bước 2 để kiểm tra vấn đề thuế GTGT.</p>
        
        <div class="form-group">
            <label>Tên khách hàng *</label>
            <input type="text" id="customerName" placeholder="Tên công ty hoặc cá nhân" value="ABC Company">
        </div>
        
        <div class="form-group">
            <label>Loại dịch vụ *</label>
            <select id="serviceType">
                <option value="">Chọn loại dịch vụ</option>
                <option value="consulting" selected>Tư vấn</option>
                <option value="design">Thiết kế</option>
                <option value="development">Phát triển</option>
                <option value="other">Khác</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Thuế suất GTGT</label>
            <select id="vatRate" onchange="updateDebug()">
                <option value="5" selected>5%</option>
                <option value="10">10%</option>
                <option value="0">0%</option>
            </select>
        </div>
        
        <button class="btn" onclick="collectData()">Thu thập dữ liệu (Giống bước 3)</button>
        <button class="btn" onclick="resetTest()">Reset Test</button>
        
        <div class="result" id="result" style="display: none;">
            <h3>📋 Kết quả thu thập dữ liệu:</h3>
            <div id="resultContent"></div>
        </div>
        
        <div class="debug" id="debug">
            <strong>🔍 Debug Info:</strong><br>
            <span id="debugContent">Chọn thuế suất và nhấn "Thu thập dữ liệu"</span>
        </div>
    </div>

    <script>
        // Mô phỏng template configuration từ app chính
        const template = {
            id: 'service_revenue',
            title: 'Thu cung cấp dịch vụ',
            type: 'income',
            category: 'Dịch vụ',
            hasVAT: true,
            defaultVATRate: 5,
            vatOptions: [5, 10, 0],
            vatNote: 'Áp dụng phương pháp tính thuế GTGT trực tiếp'
        };

        // Mô phỏng hàm collectStep2Data() từ app chính
        function collectData() {
            const data = {};
            
            // Thu thập dữ liệu giống như trong app
            data.customerName = document.getElementById('customerName').value.trim();
            data.serviceType = document.getElementById('serviceType').value;
            
            // VAT handling - đây là phần quan trọng
            if (template.hasVAT) {
                const vatRateEl = document.getElementById('vatRate');
                if (vatRateEl) {
                    data.vatRate = parseFloat(vatRateEl.value);
                    console.log('Collected VAT Rate:', data.vatRate, 'from element value:', vatRateEl.value);
                } else {
                    console.log('VAT Rate element not found!');
                }
            }
            
            // Hiển thị kết quả
            displayResult(data);
            updateDebug();
        }
        
        function displayResult(data) {
            const resultDiv = document.getElementById('result');
            const contentDiv = document.getElementById('resultContent');
            
            let vatInfo = '';
            if (template.hasVAT && data.vatRate !== undefined && data.vatRate !== null) {
                vatInfo = `<div><strong>Thuế GTGT:</strong> ${data.vatRate}%</div>`;
            }
            
            contentDiv.innerHTML = `
                <div><strong>Khách hàng:</strong> ${data.customerName}</div>
                <div><strong>Loại dịch vụ:</strong> ${data.serviceType}</div>
                <div><strong>Danh mục:</strong> ${template.category}</div>
                ${vatInfo}
                <hr style="margin: 15px 0;">
                <div style="font-family: monospace; background: #f1f1f1; padding: 10px; border-radius: 4px;">
                    <strong>Raw Data:</strong><br>
                    ${JSON.stringify(data, null, 2)}
                </div>
            `;
            
            resultDiv.style.display = 'block';
        }
        
        function updateDebug() {
            const vatRateEl = document.getElementById('vatRate');
            const selectedValue = vatRateEl.value;
            const selectedText = vatRateEl.options[vatRateEl.selectedIndex].text;
            
            const debugContent = `
                Selected Value: "${selectedValue}"<br>
                Selected Text: "${selectedText}"<br>
                Element ID: "${vatRateEl.id}"<br>
                parseFloat(value): ${parseFloat(selectedValue)}<br>
                Template defaultVATRate: ${template.defaultVATRate}<br>
                Template hasVAT: ${template.hasVAT}
            `;
            
            document.getElementById('debugContent').innerHTML = debugContent;
        }
        
        function resetTest() {
            document.getElementById('customerName').value = 'ABC Company';
            document.getElementById('serviceType').value = 'consulting';
            document.getElementById('vatRate').value = '5';
            document.getElementById('result').style.display = 'none';
            updateDebug();
        }
        
        // Initialize debug info
        updateDebug();
        
        // Log template info
        console.log('Template Configuration:', template);
    </script>
</body>
</html>
