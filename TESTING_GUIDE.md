# 🧪 Hướng dẫn Test Ứng dụng Kế toán - <PERSON><PERSON><PERSON> bản Cuối cùng

## 📂 File cần mở

**Mở file:** `client/public/standalone.html`

**Đường dẫn đầy đủ:** `c:\Users\<USER>\Documents\augment-projects\app_ke_toan_133\client\public\standalone.html`

## 🎯 Các nghiệp vụ cần test

### 📈 **THU NHẬP (4 nghiệp vụ)**

#### **1. 💼 Thu cung cấp dịch vụ** (CÓ THUẾ 5%)
**Cách test:**
1. Click "💼 Thu cung cấp dịch vụ"
2. **Bước 1:** <PERSON><PERSON><PERSON><PERSON> mô tả, số tiền, ngày
3. **Bước 2:** 
   - Tên kh<PERSON>ch hàng: "ABC Company"
   - <PERSON>ại dịch vụ: Chọn "Tư vấn"
   - Thuế GTGT: Kiểm tra mặc định **5%** (c<PERSON> thể chọn 10%, 0%)
4. **Bước 3:** <PERSON><PERSON><PERSON> nh<PERSON>n thông tin
5. **Kết quả:** Giao dịch được tạo với thuế GTGT 5%

#### **2. 💰 Thu lãi tiền gửi** (KHÔNG THUẾ)
**Cách test:**
1. Click "💰 Thu lãi tiền gửi"
2. **Bước 1:** Nhập mô tả, số tiền, ngày
3. **Bước 2:**
   - Ngân hàng/Tổ chức: "Vietcombank"
   - Thuế GTGT: Kiểm tra hiển thị box xám "Lãi tiền gửi không chịu thuế GTGT"
4. **Bước 3:** Xác nhận thông tin
5. **Kết quả:** Giao dịch được tạo không có thuế GTGT

#### **3. 🏦 Thu nhận vốn góp** (KHÔNG THUẾ)
**Cách test:**
1. Click "🏦 Thu nhận vốn góp"
2. **Bước 1:** Nhập mô tả, số tiền, ngày
3. **Bước 2:**
   - Tên người góp vốn: "Nguyễn Văn A"
   - Loại vốn góp: Chọn "Vốn góp ban đầu"
   - Thuế GTGT: Kiểm tra hiển thị box xám "Vốn góp không chịu thuế GTGT"
4. **Bước 3:** Xác nhận thông tin
5. **Kết quả:** Giao dịch được tạo không có thuế GTGT

#### **4. ➕ Thu khác** (CÓ THUẾ 5%)
**Cách test:**
1. Click "➕ Thu khác"
2. **Bước 1:** Nhập mô tả, số tiền, ngày
3. **Bước 2:**
   - Ghi chú: "Thu nhập khác từ dịch vụ"
   - Thuế GTGT: Kiểm tra mặc định **5%** (có thể chọn 10%, 0%)
4. **Bước 3:** Xác nhận thông tin
5. **Kết quả:** Giao dịch được tạo với thuế GTGT 5%

### 📉 **CHI PHÍ (4 nghiệp vụ)**

#### **5. 👥 Chi trả lương** (KHÔNG THUẾ)
**Cách test:**
1. Click "👥 Chi trả lương"
2. **Bước 1:** Nhập mô tả, số tiền, ngày
3. **Bước 2:**
   - Tên nhân viên: "Trần Thị B"
   - Loại lương: Chọn "Lương cơ bản"
   - Kỳ lương: "12/2024"
   - Thuế GTGT: Kiểm tra hiển thị box xám "Doanh nghiệp không khấu trừ thuế GTGT đầu vào"
4. **Bước 3:** Xác nhận thông tin
5. **Kết quả:** Giao dịch được tạo không có thuế GTGT

#### **6. 🏛️ Chi đóng thuế** (KHÔNG THUẾ)
**Cách test:**
1. Click "🏛️ Chi đóng thuế"
2. **Bước 1:** Nhập mô tả, số tiền, ngày
3. **Bước 2:**
   - Loại thuế: Chọn "Thuế GTGT"
   - Kỳ thuế: "Q4/2024"
   - Thuế GTGT: Kiểm tra hiển thị box xám "Doanh nghiệp không khấu trừ thuế GTGT đầu vào"
4. **Bước 3:** Xác nhận thông tin
5. **Kết quả:** Giao dịch được tạo không có thuế GTGT

#### **7. 🛡️ Chi đóng BHXH** (KHÔNG THUẾ)
**Cách test:**
1. Click "🛡️ Chi đóng BHXH"
2. **Bước 1:** Nhập mô tả, số tiền, ngày
3. **Bước 2:**
   - Loại bảo hiểm: Chọn "Tất cả các loại"
   - Kỳ đóng: "12/2024"
   - Số lượng nhân viên: "5"
   - Thuế GTGT: Kiểm tra hiển thị box xám "Doanh nghiệp không khấu trừ thuế GTGT đầu vào"
4. **Bước 3:** Xác nhận thông tin
5. **Kết quả:** Giao dịch được tạo không có thuế GTGT

#### **8. ➖ Chi khác** (KHÔNG THUẾ)
**Cách test:**
1. Click "➖ Chi khác"
2. **Bước 1:** Nhập mô tả, số tiền, ngày
3. **Bước 2:**
   - Ghi chú: "Chi phí khác"
   - Thuế GTGT: Kiểm tra hiển thị box xám "Doanh nghiệp không khấu trừ thuế GTGT đầu vào"
4. **Bước 3:** Xác nhận thông tin
5. **Kết quả:** Giao dịch được tạo không có thuế GTGT

## ✅ Checklist Test

### **Giao diện:**
- [ ] Hiển thị đúng 4 card Thu nhập
- [ ] Hiển thị đúng 4 card Chi phí
- [ ] Icon và mô tả phù hợp với từng nghiệp vụ

### **Wizard hoạt động:**
- [ ] Có thể mở wizard cho tất cả 8 nghiệp vụ
- [ ] Có thể đóng wizard bằng nút X
- [ ] Có thể đóng wizard bằng click overlay
- [ ] Có thể điều hướng Tiếp theo/Quay lại

### **Thuế GTGT:**
- [ ] Thu dịch vụ: Mặc định 5%, có thể chọn 10%, 0%
- [ ] Thu khác: Mặc định 5%, có thể chọn 10%, 0%
- [ ] Thu lãi tiền: Box xám "Lãi tiền gửi không chịu thuế GTGT"
- [ ] Thu vốn góp: Box xám "Vốn góp không chịu thuế GTGT"
- [ ] Tất cả Chi phí: Box xám "Doanh nghiệp không khấu trừ thuế GTGT đầu vào"

### **Thông tin chi tiết:**
- [ ] Thu dịch vụ: Khách hàng + Loại dịch vụ
- [ ] Thu vốn góp: Người góp + Loại vốn góp
- [ ] Chi lương: Nhân viên + Loại lương + Kỳ lương
- [ ] Chi thuế: Loại thuế + Kỳ thuế
- [ ] Chi BHXH: Loại BH + Kỳ đóng + Số NV

### **Bước 3 - Xác nhận:**
- [ ] Hiển thị đầy đủ thông tin giao dịch
- [ ] Hiển thị đúng thông tin thuế GTGT
- [ ] Hiển thị đúng thông tin chi tiết từng nghiệp vụ

### **Hoàn thành:**
- [ ] Tạo giao dịch thành công
- [ ] Hiển thị thông báo thành công
- [ ] Chuyển về tab Giao dịch
- [ ] Giao dịch xuất hiện trong lịch sử

### **Dashboard:**
- [ ] Cập nhật tổng thu/chi
- [ ] Cập nhật lợi nhuận
- [ ] Cập nhật số giao dịch
- [ ] Hiển thị giao dịch gần đây

## 🐛 Các lỗi có thể gặp

### **Lỗi thường gặp:**
1. **Wizard không mở:** Kiểm tra console browser (F12)
2. **Không thể đóng wizard:** Kiểm tra CSS class `.hidden`
3. **Thuế GTGT không đúng:** Kiểm tra template configuration
4. **Thông tin không hiển thị:** Kiểm tra data collection

### **Cách debug:**
1. **Mở Developer Tools:** F12
2. **Xem Console:** Kiểm tra lỗi JavaScript
3. **Xem Network:** Kiểm tra tải file
4. **Xem Elements:** Kiểm tra HTML/CSS

## 📊 Kết quả mong đợi

### **Sau khi test đầy đủ 8 nghiệp vụ:**

**Dashboard sẽ hiển thị:**
```
📊 DASHBOARD:
Tổng thu tháng này: [Tổng 4 giao dịch thu]
Tổng chi tháng này: [Tổng 4 giao dịch chi]
Lợi nhuận ròng: [Thu - Chi]
Số giao dịch: 8
```

**Lịch sử giao dịch:**
- **Thu nhập:** 4 giao dịch (2 có thuế 5%, 2 không thuế)
- **Chi phí:** 4 giao dịch (tất cả không thuế)

**Thuế GTGT tổng:**
- **Phải nộp:** Chỉ từ Thu dịch vụ + Thu khác (nếu có)
- **Được khấu trừ:** 0 VND (không khấu trừ)

## 🎉 Kết luận

**Nếu tất cả test case đều PASS:**
✅ Ứng dụng hoạt động hoàn hảo cho doanh nghiệp dịch vụ
✅ Thuế GTGT được tính đúng theo phương pháp trực tiếp
✅ 8 nghiệp vụ cơ bản đầy đủ và chính xác
✅ Giao diện thân thiện, dễ sử dụng

**👉 Ứng dụng sẵn sàng đưa vào sử dụng thực tế!**
